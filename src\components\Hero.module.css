/* Hero component optimizations */
.heroSection {
  contain: layout style paint;
  will-change: transform;
}

.parallaxContainer {
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

.floatingElement {
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

.heroImage {
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

.heroImage:hover {
  transform: translateZ(0) scale(1.05); /* Maintain hardware acceleration on hover */
}

/* Optimize animations for better performance */
@media (prefers-reduced-motion: no-preference) {
  .floatingElement {
    animation: float 3s ease-in-out infinite;
  }
}

@media (prefers-reduced-motion: reduce) {
  .floatingElement {
    animation: none;
  }
}

/* Improve text rendering */
.heroTitle {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.heroDescription {
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
