import { Download, Play, Shield } from "lucide-react";
import { useState, useEffect, useCallback, memo } from "react";
import heroImage1 from "../assets/hero-image1.png";
import heroImage2 from "../assets/hero-image2.png";
import heroImage3 from "../assets/hero-image3.png";

// Floating animation bubbles extracted and memoized
const FloatingBubbles = memo(() => (
  <>
    <div className="absolute top-6 left-4 w-3 h-3 bg-green-400 rounded-full animate-float opacity-60 sm:top-16 sm:left-12 sm:w-4 sm:h-4" />
    <div className="absolute bottom-10 right-6 w-4 h-4 bg-emerald-300 rounded-full animate-float opacity-50 sm:bottom-24 sm:right-24 sm:w-6 sm:h-6" style={{ animationDelay: '1s' }} />
    <div className="absolute top-1/2 right-2 w-2 h-2 bg-green-300 rounded-full animate-float opacity-70 sm:top-1/3 sm:right-10 sm:w-3 sm:h-3" style={{ animationDelay: '2s' }} />
    <div className="absolute top-10 left-1/2 w-3 h-3 bg-lime-400 rounded-full animate-float opacity-40 sm:top-20 sm:left-1/2 sm:w-5 sm:h-5" style={{ animationDelay: '0.5s', transform: 'translateX(-50%)' }} />
    <div className="absolute bottom-1/4 left-1/5 w-2 h-2 bg-green-500 rounded-full animate-float opacity-60 sm:left-1/4 sm:w-2.5 sm:h-2.5" style={{ animationDelay: '1.5s' }} />
    <div className="absolute top-1/2 right-1/4 w-3 h-3 bg-lime-400 rounded-full animate-float opacity-40 sm:w-5 sm:h-5" style={{ animationDelay: '2.5s' }} />
  </>
));

function throttle<T extends unknown[]>(fn: (...args: T) => void, wait: number) {
  let last = 0;
  return (...args: T) => {
    const now = Date.now();
    if (now - last > wait) {
      last = now;
      fn(...args);
    }
  };
}

const Hero = () => {
  const [scrollY, setScrollY] = useState(0);

  // Throttled scroll handler
  const handleScroll = useCallback(() => {
    setScrollY(window.scrollY);
  }, [setScrollY]);

  useEffect(() => {
    const throttled = throttle(handleScroll, 50);
    window.addEventListener("scroll", throttled);
    return () => {
      window.removeEventListener("scroll", throttled);
    };
  }, [handleScroll]);

  return (
    <section
      id="hero"
      className="relative flex min-h-screen items-center justify-center overflow-hidden"
      aria-label="Hero section"
    >
      <div
        className="absolute inset-0 z-0"
        style={{
          transform: `translateY(${scrollY * 0.5}px)`,
        }}
        aria-hidden="true"
      >
        <FloatingBubbles />
      </div>

      {/* Hero Content */}
      <div className="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-10 sm:pt-20 sm:pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="inline-flex items-center bg-emerald-100 text-emerald-800 px-4 py-2 rounded-full text-sm font-medium">
                <Shield className="h-4 w-4 mr-2" aria-hidden="true" />
                Blockchain-Powered Transparency
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-black leading-tight">
                Empowering Agriculture with{' '}
                <span className="bg-gradient-to-r from-emerald-600 to-amber-600 bg-clip-text text-transparent relative">
                  Stable Finance
                  <span className="absolute -bottom-2 left-0 w-full h-1 bg-[#2D4D31]/20 rounded-full" aria-hidden="true"></span>
                </span>
              </h1>
              <p className="text-base sm:text-lg md:text-xl text-gray-600 max-w-xl">
                Bringing stability, transparency, and growth to every crop and trade.
              </p>
            </div>

            {/* Primary CTAs */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="flex items-center justify-center px-6 py-3 sm:px-8 sm:py-4 bg-[#2D4D31] text-white rounded-lg hover:bg-[#2D4D31]/90 transition-all duration-300 font-medium border-2 border-[#2D4D31] border-dotted border-opacity-70 group focus:outline-none focus:ring-2 focus:ring-emerald-500" aria-label="View Whitepaper">
                <Download className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" aria-hidden="true" />
                View Whitepaper
              </button>
              <button 
                // onClick={() => setShowVideo(true)}
                className="flex items-center justify-center px-6 py-3 sm:px-8 sm:py-4 bg-white text-[#2D4D31] rounded-lg hover:bg-gray-50 transition-all duration-300 font-medium border-2 border-[#2D4D31] group focus:outline-none focus:ring-2 focus:ring-emerald-500"
                aria-label="Watch How It Works"
              >
                <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" aria-hidden="true" />
                Watch How It Works
              </button>
            </div>
          </div>

          {/* Visual */}
          <div className="relative w-full h-72 sm:h-96 lg:h-[36rem] flex items-center justify-center mt-8 lg:mt-0">
            {/* Collage of images */}
            <img
              src={heroImage1}
              alt="Farmers in a field"
              className="absolute left-0 top-0 h-32 sm:h-48 md:h-60 object-cover rounded-xl shadow-lg rotate-[-8deg] z-20"
              style={{ boxShadow: '0 8px 32px rgba(44, 62, 80, 0.18)' }}
              loading="lazy"
            />
            <img
              src={heroImage2}
              alt="Agricultural produce"
              className="absolute right-0 top-8 sm:top-12 h-36 sm:h-56 md:h-72 object-cover rounded-xl shadow-xl rotate-[10deg] z-10"
              style={{ boxShadow: '0 8px 32px rgba(44, 62, 80, 0.15)' }}
              loading="lazy"
            />
            <img
              src={heroImage3}
              alt="Blockchain technology in agriculture"
              className="absolute left-1/2 bottom-0 h-32 sm:h-48 md:h-64 object-cover rounded-xl shadow-2xl -translate-x-1/2 rotate-2 z-30"
              style={{ boxShadow: '0 8px 32px rgba(44, 62, 80, 0.22)' }}
              loading="lazy"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;